#!/usr/bin/env python3
"""
EM-4/3 Doubling Example - Optimized Edition
==========================================
Train a cellular automaton to perform the doubling operation (x -> 2x)
using the EM-4/3 system with genetic algorithm optimization.

This example demonstrates:
- Creating EM-4/3 genomes with rules and programmes
- Setting up batch simulation with Numba acceleration
- Training with genetic algorithm
- Population-parallel fitness evaluation for maximum speed
- Continuous error-based fitness for smoother optimization

Usage:
    # Basic usage with optimized defaults
    python examples/em43_doubling.py

    # Maximum speed with population-parallel evaluation
    python examples/em43_doubling.py --use-population-parallel
    python examples/em43_doubling.py --use-population-parallel --use-continuous-fitness

    # Match fast code parameters exactly
    python examples/em43_doubling.py --population 20000 --generations 300 --max-steps 800

    # Custom experiments
    python examples/em43_doubling.py --population 10000 --generations 200 --use-continuous-fitness
    python examples/em43_doubling.py --input-range-start 1 --input-range-end 50 --window-size 300
"""

import argparse
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import time

try:
    import numba as nb
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    print("Warning: Numba not available, population-parallel evaluation disabled")

# Import emergent_models components
from emergent_models.rules.em43 import EM43Rule, EM43Genome
from emergent_models.simulators.numba_simulator import NumbaSimulator
from emergent_models.optimizers.genetic import GAOptimizer
from emergent_models.data.mathematical import DoublingDataset
from emergent_models.data.dataloader import CADataLoader
from emergent_models.losses.distance import HammingLoss
from emergent_models.training.trainer import CATrainer, create_accuracy_validator
from emergent_models.utils.visualization import plot_fitness_curve
from emergent_models.training.checkpointing import save_genome, load_genome


# Population-parallel fitness evaluation functions
if NUMBA_AVAILABLE:
    @nb.njit(parallel=True, fastmath=True, cache=True)
    def _evaluate_population_parallel_doubling(
        rules_array: np.ndarray,
        programmes_array: np.ndarray,
        inputs: np.ndarray,
        targets: np.ndarray,
        window: int,
        max_steps: int,
        halt_thresh: float,
        sparsity_penalty: float,
        use_continuous_fitness: bool
    ) -> np.ndarray:
        """
        Evaluate entire population in parallel for doubling task.

        Parameters
        ----------
        rules_array : (P, 64) uint8 - Rule lookup tables for each genome
        programmes_array : (P, L) uint8 - Programmes for each genome
        inputs : (N,) int64 - Input values
        targets : (N,) int64 - Target output values (2x inputs)
        window : int - Tape length
        max_steps : int - Maximum simulation steps
        halt_thresh : float - Halting threshold
        sparsity_penalty : float - Sparsity penalty coefficient
        use_continuous_fitness : bool - Use continuous error vs binary accuracy

        Returns
        -------
        fitness : (P,) float32 - Fitness scores for each genome
        """
        P = rules_array.shape[0]  # Population size
        L = programmes_array.shape[1]  # Programme length
        N_inputs = inputs.shape[0]

        fitness = np.empty(P, dtype=np.float32)

        # Evaluate each genome in parallel
        for p in nb.prange(P):
            rule = rules_array[p]
            programme = programmes_array[p]

            # Simulate all inputs for this genome
            outputs = np.full(N_inputs, -10, dtype=np.int32)

            for i in range(N_inputs):
                input_val = inputs[i]

                # Create initial state with positional encoding (like fast code)
                state = np.zeros(window, dtype=np.uint8)

                # Write programme
                for j in range(L):
                    state[j] = programme[j]

                # Write separator (BB)
                state[L] = 3      # Blue
                state[L + 1] = 3  # Blue

                # Write input as positional encoding: 0^(input+1) R 0 (matches old version)
                beacon_pos = L + 2 + input_val + 1
                if beacon_pos < window:
                    state[beacon_pos] = 2  # Red beacon

                # Simulate
                halted = False
                for step in range(max_steps):
                    if halted:
                        break

                    next_state = np.zeros(window, dtype=np.uint8)

                    # Apply CA rules (boundary cells stay 0)
                    for x in range(1, window - 1):
                        left = state[x - 1]
                        center = state[x]
                        right = state[x + 1]
                        idx = (left << 4) | (center << 2) | right
                        next_state[x] = rule[idx]

                    state = next_state

                    # Check halting condition
                    live_count = 0
                    blue_count = 0
                    for x in range(window):
                        if state[x] != 0:
                            live_count += 1
                            if state[x] == 3:  # Blue
                                blue_count += 1

                    if live_count > 0 and blue_count / live_count >= halt_thresh:
                        halted = True

                # Decode output (find rightmost red beacon position)
                if halted:
                    rightmost_red = -1
                    for x in range(window - 1, -1, -1):
                        if state[x] == 2:  # Red
                            rightmost_red = x
                            break

                    if rightmost_red != -1:
                        # Calculate output like old version: rpos - (L + 3)
                        # This accounts for programme + separator (BB) + 1 zero before R
                        outputs[i] = rightmost_red - (L + 3)

            # Calculate fitness for this genome
            if use_continuous_fitness:
                # Continuous fitness: mean absolute error (like fast code)
                total_error = 0.0
                valid_outputs = 0
                for i in range(N_inputs):
                    if outputs[i] >= 0:
                        total_error += abs(outputs[i] - targets[i])
                        valid_outputs += 1

                if valid_outputs > 0:
                    avg_error = total_error / valid_outputs
                    accuracy_score = -avg_error  # Negative error (higher is better)
                else:
                    accuracy_score = -1000.0  # Large penalty for no valid outputs
            else:
                # Binary accuracy fitness
                correct = 0
                for i in range(N_inputs):
                    if outputs[i] >= 0 and outputs[i] == targets[i]:
                        correct += 1
                accuracy_score = correct / N_inputs

            # Add sparsity penalty
            sparsity = 0
            for j in range(L):
                if programme[j] != 0:
                    sparsity += 1
            sparsity_pen = sparsity_penalty * sparsity / L

            fitness[p] = accuracy_score - sparsity_pen

        return fitness


def create_population_parallel_fitness_function(
    simulator: NumbaSimulator,
    input_range=(1, 30),
    use_continuous_fitness=False,
    sparsity_penalty=0.01
):
    """Create a population-parallel fitness function for maximum speed"""
    if not NUMBA_AVAILABLE:
        print("Warning: Numba not available, falling back to standard fitness function")
        return create_fitness_function(simulator, input_range)

    inputs = np.array(list(range(input_range[0], input_range[1] + 1)), dtype=np.int64)
    targets = np.array([2 * x for x in inputs], dtype=np.int64)  # Doubling task

    def population_fitness_fn(population):
        """Evaluate entire population at once"""
        if not population:
            return []

        # Extract rules and programmes from population
        pop_size = len(population)
        prog_length = len(population[0].programme)

        rules_array = np.zeros((pop_size, 64), dtype=np.uint8)
        programmes_array = np.zeros((pop_size, prog_length), dtype=np.uint8)

        for i, genome in enumerate(population):
            rules_array[i] = genome.rule.get_rule_array()
            programmes_array[i] = genome.programme

        # Use population-parallel evaluation
        fitness_scores = _evaluate_population_parallel_doubling(
            rules_array, programmes_array, inputs, targets,
            simulator.window, simulator.max_steps, simulator.halt_thresh,
            sparsity_penalty, use_continuous_fitness
        )

        return fitness_scores.tolist()

    return population_fitness_fn


def create_fitness_function(simulator: NumbaSimulator, input_range=(1, 30)):
    """Create a fitness function for the doubling task"""
    inputs = list(range(input_range[0], input_range[1] + 1))
    targets = [2 * x for x in inputs]
    
    def fitness_fn(genome: EM43Genome, sim: NumbaSimulator) -> float:
        """Evaluate genome fitness on doubling task"""
        try:
            outputs = sim.simulate_batch(genome, inputs)
            
            # Calculate accuracy
            correct = 0
            total = len(inputs)
            
            for i, (target, output) in enumerate(zip(targets, outputs)):
                if output >= 0 and output == target:
                    correct += 1
            
            accuracy = correct / total
            
            # Add sparsity penalty (encourage simpler programmes)
            sparsity_penalty = 0.01 * np.count_nonzero(genome.programme) / len(genome.programme)
            
            # Fitness is accuracy minus sparsity penalty
            fitness = accuracy - sparsity_penalty
            
            return fitness
            
        except Exception as e:
            print(f"Error evaluating genome: {e}")
            return -1.0
    
    return fitness_fn


def train_with_population_parallel(optimizer, population_fitness_fn, validation_fn, config):
    """Custom training loop with population-parallel fitness evaluation"""
    from tqdm import tqdm

    history = {
        'best_fitness': [],
        'mean_fitness': [],
        'std_fitness': [],
        'generation_times': []
    }

    best_genome = None
    best_fitness = float('-inf')

    pbar = tqdm(range(config['generations']), desc="Training")

    for generation in pbar:
        gen_start_time = time.time()

        # Evaluate entire population at once
        fitness_scores = population_fitness_fn(optimizer.population)

        # Update best genome
        max_fitness_idx = np.argmax(fitness_scores)
        if fitness_scores[max_fitness_idx] > best_fitness:
            best_fitness = fitness_scores[max_fitness_idx]
            best_genome = optimizer.population[max_fitness_idx]
            if hasattr(best_genome, 'clone'):
                best_genome = best_genome.clone()
            best_genome.fitness = best_fitness

        # Record history
        history['best_fitness'].append(np.max(fitness_scores))
        history['mean_fitness'].append(np.mean(fitness_scores))
        history['std_fitness'].append(np.std(fitness_scores))

        # Evolution step
        optimizer.step(fitness_scores)

        gen_time = time.time() - gen_start_time
        history['generation_times'].append(gen_time)

        # Update progress bar
        pbar.set_postfix({
            'best': f"{best_fitness:.4f}",
            'mean': f"{np.mean(fitness_scores):.4f}",
            'time': f"{gen_time:.2f}s"
        })

        # Early stopping check
        if not config['disable_early_stopping'] and validation_fn:
            # Create a dummy simulator for validation
            from emergent_models.simulators.numba_simulator import NumbaSimulator
            dummy_sim = NumbaSimulator(window=config['window_size'], max_steps=config['max_steps'], halt_thresh=config['halt_thresh'])
            validation_accuracy = validation_fn(best_genome, dummy_sim)
            if validation_accuracy >= config['early_stopping_threshold']:
                print(f"\nEarly stopping at generation {generation + 1}: "
                      f"validation accuracy {validation_accuracy:.1%} >= {config['early_stopping_threshold']:.1%}")
                break

        # Checkpoint saving
        if (generation + 1) % config['checkpoint_every'] == 0:
            print(f"\nGeneration {generation + 1}: best={best_fitness:.4f}, "
                  f"mean={np.mean(fitness_scores):.4f}, time={gen_time:.2f}s")

    return best_genome, history


def main():
    """Main training loop"""
    parser = argparse.ArgumentParser(description='Train EM-4/3 doubling with optimized parameters')

    # Core parameters (matching fast code defaults)
    parser.add_argument('--population', type=int, default=20000,
                       help='Population size (default: 20000 - matches fast code)')
    parser.add_argument('--generations', type=int, default=300,
                       help='Number of generations (default: 300 - matches fast code)')

    # CA parameters (matching fast code)
    parser.add_argument('--programme-length', type=int, default=10,
                       help='Programme length (default: 10 - matches fast code)')
    parser.add_argument('--window-size', type=int, default=200,
                       help='Window size (default: 200 - matches fast code)')
    parser.add_argument('--max-steps', type=int, default=800,
                       help='Maximum simulation steps (default: 800 - matches fast code)')
    parser.add_argument('--halt-thresh', type=float, default=0.50,
                       help='Halt threshold for simulation (default: 0.50)')

    # Genetic algorithm parameters (matching fast code)
    parser.add_argument('--mutation-rate', type=float, default=0.03,
                       help='Mutation rate (default: 0.03 - matches fast code)')
    parser.add_argument('--programme-mutation-rate', type=float, default=0.08,
                       help='Programme mutation rate (default: 0.08 - matches fast code)')
    parser.add_argument('--elite-fraction', type=float, default=0.10,
                       help='Elite fraction (default: 0.10 - matches fast code)')
    parser.add_argument('--tournament-size', type=int, default=3,
                       help='Tournament size (default: 3)')
    parser.add_argument('--random-immigrant-rate', type=float, default=0.20,
                       help='Random immigrant rate (default: 0.20 - matches fast code)')

    # Input range parameters
    parser.add_argument('--input-range-start', type=int, default=1,
                       help='Input range start (default: 1)')
    parser.add_argument('--input-range-end', type=int, default=30,
                       help='Input range end (default: 30 - matches fast code)')

    # Early stopping parameters
    parser.add_argument('--early-stopping-threshold', type=float, default=1.0,
                       help='Early stopping accuracy threshold (default: 1.0)')
    parser.add_argument('--disable-early-stopping', action='store_true',
                       help='Disable early stopping')

    # Training parameters
    parser.add_argument('--checkpoint-every', type=int, default=50,
                       help='Checkpoint frequency in generations (default: 50)')

    # Fitness evaluation parameters
    parser.add_argument('--use-population-parallel', action='store_true',
                       help='Use population-parallel fitness evaluation for maximum speed')
    parser.add_argument('--use-continuous-fitness', action='store_true',
                       help='Use continuous error-based fitness instead of binary accuracy (matches fast code)')
    parser.add_argument('--sparsity-penalty', type=float, default=0.01,
                       help='Sparsity penalty coefficient (default: 0.01 - matches fast code)')

    args = parser.parse_args()

    print("EM-4/3 Doubling Task Training (Optimized Edition)")
    print("=" * 60)

    # Configuration from CLI arguments
    config = {
        'population_size': args.population,
        'generations': args.generations,
        'programme_length': args.programme_length,
        'window_size': args.window_size,
        'max_steps': args.max_steps,
        'halt_thresh': args.halt_thresh,
        'mutation_rate': args.mutation_rate,
        'programme_mutation_rate': args.programme_mutation_rate,
        'elite_fraction': args.elite_fraction,
        'tournament_size': args.tournament_size,
        'random_immigrant_rate': args.random_immigrant_rate,
        'input_range': (args.input_range_start, args.input_range_end),
        'early_stopping_threshold': args.early_stopping_threshold,
        'disable_early_stopping': args.disable_early_stopping,
        'checkpoint_every': args.checkpoint_every,
        'use_population_parallel': args.use_population_parallel,
        'use_continuous_fitness': args.use_continuous_fitness,
        'sparsity_penalty': args.sparsity_penalty
    }

    print(f"Configuration:")
    print(f"- Population size: {config['population_size']}")
    print(f"- Generations: {config['generations']}")
    print(f"- Programme length: {config['programme_length']}")
    print(f"- Window size: {config['window_size']}")
    print(f"- Max steps: {config['max_steps']}")
    print(f"- Halt threshold: {config['halt_thresh']}")
    print(f"- Input range: {config['input_range']}")
    print(f"- Mutation rate: {config['mutation_rate']}")
    print(f"- Programme mutation rate: {config['programme_mutation_rate']}")
    print(f"- Elite fraction: {config['elite_fraction']}")
    print(f"- Tournament size: {config['tournament_size']}")
    print(f"- Random immigrant rate: {config['random_immigrant_rate']}")
    if not config['disable_early_stopping']:
        print(f"- Early stopping threshold: {config['early_stopping_threshold']}")
    print(f"- Checkpoint every: {config['checkpoint_every']} generations")
    print(f"- Population-parallel evaluation: {config['use_population_parallel']}")
    print(f"- Continuous fitness: {config['use_continuous_fitness']}")
    print(f"- Sparsity penalty: {config['sparsity_penalty']}")
    print()
    
    # Create simulator
    simulator = NumbaSimulator(
        window=config['window_size'],
        max_steps=config['max_steps'],
        halt_thresh=config['halt_thresh']
    )

    # Create optimizer
    optimizer = GAOptimizer(
        population_size=config['population_size'],
        mutation_rate=config['mutation_rate'],
        programme_mutation_rate=config['programme_mutation_rate'],
        elite_fraction=config['elite_fraction'],
        tournament_size=config['tournament_size'],
        random_immigrant_rate=config['random_immigrant_rate']
    )

    # Initialize population
    print("Initializing population...")
    optimizer.initialize_population(programme_length=config['programme_length'])

    # Create fitness function based on configuration
    if config['use_population_parallel'] and NUMBA_AVAILABLE:
        print("Using population-parallel fitness evaluation for maximum speed!")
        if config['use_continuous_fitness']:
            print("Using continuous error-based fitness for smoother optimization (matches fast code)")
        else:
            print("Using binary accuracy-based fitness")

        population_fitness_fn = create_population_parallel_fitness_function(
            simulator, config['input_range'],
            use_continuous_fitness=config['use_continuous_fitness'],
            sparsity_penalty=config['sparsity_penalty']
        )

        # Wrapper to make it compatible with trainer
        def fitness_fn(genome, sim):
            # This shouldn't be called when using population-parallel
            return create_fitness_function(sim, config['input_range'])(genome, sim)
    else:
        if config['use_population_parallel']:
            print("Warning: Population-parallel evaluation requested but Numba not available")
        print("Using standard individual fitness evaluation")
        fitness_fn = create_fitness_function(simulator, config['input_range'])
        population_fitness_fn = None

    # Create validation function for early stopping
    test_inputs = list(range(config['input_range'][0], min(config['input_range'][0] + 10, config['input_range'][1] + 1)))
    test_targets = [2 * x for x in test_inputs]  # Expected outputs
    validation_fn = create_accuracy_validator(test_inputs, test_targets)

    # Training with configurable early stopping
    print("Starting training...")
    if not config['disable_early_stopping']:
        print(f"Early stopping enabled: will stop if {config['early_stopping_threshold']:.1%} accuracy is reached")
    else:
        print("Early stopping disabled")
    start_time = time.time()

    # Use custom training loop for population-parallel evaluation
    if config['use_population_parallel'] and NUMBA_AVAILABLE:
        print("Using custom training loop with population-parallel evaluation")
        best_genome, history = train_with_population_parallel(
            optimizer, population_fitness_fn, validation_fn, config
        )

        # Create a mock trainer object for compatibility
        class MockTrainer:
            def __init__(self, best_genome, best_fitness, history):
                self.best_genome = best_genome
                self.best_fitness = best_genome.fitness if hasattr(best_genome, 'fitness') else 0.0
                self.history = history

        trainer = MockTrainer(best_genome, best_genome.fitness if hasattr(best_genome, 'fitness') else 0.0, history)
    else:
        # Use standard trainer
        trainer = CATrainer(simulator, optimizer, HammingLoss(), verbose=True)

        if config['disable_early_stopping']:
            trainer.fit(
                fitness_fn=fitness_fn,
                epochs=config['generations'],
                checkpoint_every=config['checkpoint_every']
            )
        else:
            trainer.fit(
                fitness_fn=fitness_fn,
                epochs=config['generations'],
                checkpoint_every=config['checkpoint_every'],
                early_stopping_threshold=config['early_stopping_threshold'],
                early_stopping_metric="accuracy",
                validation_fn=validation_fn
            )

    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time:.2f} seconds")

    # Results
    best_genome = trainer.best_genome
    best_fitness = trainer.best_fitness
    
    print(f"\nBest fitness: {best_fitness:.4f}")
    print(f"Best genome programme: {best_genome.programme}")

    # Test the best genome
    print("\nTesting best genome:")
    # Test on a subset of the input range for display
    test_start = config['input_range'][0]
    test_end = min(config['input_range'][0] + 15, config['input_range'][1] + 1)
    test_inputs = list(range(test_start, test_end))
    test_outputs = simulator.simulate_batch(best_genome, test_inputs)

    print("Input -> Expected -> Actual -> Status")
    print("-" * 40)
    for inp, out in zip(test_inputs, test_outputs):
        expected = 2 * inp
        status = "✓" if out == expected else "✗"
        print(f"{inp:2d} -> {expected:2d} -> {out:2d} {status}")

    # Calculate final accuracy on full input range
    full_inputs = list(range(config['input_range'][0], config['input_range'][1] + 1))
    full_outputs = simulator.simulate_batch(best_genome, full_inputs)
    correct = sum(1 for inp, out in zip(full_inputs, full_outputs) if out == 2 * inp)
    accuracy = correct / len(full_inputs)
    print(f"\nAccuracy on full input range {config['input_range']}: {accuracy:.2%} ({correct}/{len(full_inputs)})")
    
    # Save results
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)

    # Save best genome
    save_genome(best_genome, results_dir / "best_doubling_genome.json")
    print(f"Saved best genome to {results_dir / 'best_doubling_genome.json'}")

    # Plot fitness curve
    if hasattr(trainer, 'history') and trainer.history:
        plot_fitness_curve(trainer.history, save_path=results_dir / "fitness_curve.png")
        print(f"Saved fitness curve to {results_dir / 'fitness_curve.png'}")

    # Print training statistics
    if hasattr(trainer, 'get_training_stats'):
        stats = trainer.get_training_stats()
        print(f"\nTraining Statistics:")
        print(f"- Total generations: {stats['generations']}")
        print(f"- Best fitness achieved: {stats['best_fitness']:.4f}")
        print(f"- Final mean fitness: {stats['final_mean_fitness']:.4f}")
        print(f"- Average time per generation: {stats['avg_generation_time']:.2f}s")
    else:
        # For population-parallel training
        print(f"\nTraining Statistics:")
        print(f"- Total generations: {config['generations']}")
        print(f"- Best fitness achieved: {best_fitness:.4f}")
        if hasattr(trainer, 'history') and trainer.history:
            print(f"- Final mean fitness: {trainer.history['mean_fitness'][-1]:.4f}")
            print(f"- Average time per generation: {np.mean(trainer.history['generation_times']):.2f}s")

    return best_genome, trainer.history if hasattr(trainer, 'history') else None


def test_saved_genome():
    """Test a previously saved genome"""
    try:
        genome = load_genome("results/best_doubling_genome.json")
        print(f"Loaded genome with fitness: {genome.fitness:.4f}")
        
        simulator = NumbaSimulator(window=200, max_steps=256, halt_thresh=0.50)
        
        # Test on doubling task
        test_inputs = list(range(1, 21))
        outputs = simulator.simulate_batch(genome, test_inputs)
        
        print("\nTest Results:")
        print("Input -> Expected -> Actual")
        print("-" * 30)
        
        correct = 0
        for inp, out in zip(test_inputs, outputs):
            expected = 2 * inp
            status = "✓" if out == expected else "✗"
            if out == expected:
                correct += 1
            print(f"{inp:2d} -> {expected:2d} -> {out:2d} {status}")
        
        accuracy = correct / len(test_inputs)
        print(f"\nAccuracy: {accuracy:.2%}")
        
    except FileNotFoundError:
        print("No saved genome found. Run training first.")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_saved_genome()
    else:
        main()
