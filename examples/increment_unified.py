#!/usr/bin/env python3
"""
EM-4/3 Unified Increment Example
================================
Train a cellular automaton to perform the increment operation (x -> x+1)
with configurable encoding (binary or positional).

This demonstrates the proper modular architecture:
1. Encoder encodes inputs + programme → initial space state
2. Simulator simulates initial state + rules → final space state  
3. Encoder decodes final state → output
4. Fitness calculated based on decoded output

Usage:
    python examples/increment_unified.py --encoding binary
    python examples/increment_unified.py --encoding positional
"""

import argparse
import numpy as np
import time
from pathlib import Path

# Import emergent_models components
from emergent_models.rules.em43 import EM43Rule, EM43Genome
from emergent_models.simulators.numba_simulator import NumbaSimulator
from emergent_models.optimizers.genetic import GAOptimizer
from emergent_models.losses.distance import HammingLoss
from emergent_models.training.trainer import CATrainer
from emergent_models.utils.visualization import plot_fitness_curve
from emergent_models.training.checkpointing import save_genome, load_genome
from emergent_models.encoders.binary import EM43BinaryEncoder
from emergent_models.encoders.position import EM43PositionalEncoder


def create_unified_fitness_function(simulator: NumbaSimulator, encoder, input_range=(1, 15)):
    """Create a fitness function that works with any encoder"""
    inputs = list(range(input_range[0], input_range[1] + 1))
    targets = [x + 1 for x in inputs]

    def fitness_fn(genome: EM43Genome, sim: NumbaSimulator) -> float:
        """Evaluate genome fitness using batch processing for speed"""
        try:
            # Create all initial spaces at once
            initial_spaces = []
            for input_val in inputs:
                initial_space = encoder.encode_input(
                    genome.programme, input_val, sim.window
                )
                initial_spaces.append(initial_space)

            # Batch simulate all spaces at once
            final_spaces = sim.simulate_spaces(genome, initial_spaces)

            # Decode all outputs
            correct = 0
            for i, (input_val, target_val) in enumerate(zip(inputs, targets)):
                final_space = final_spaces[i]

                if isinstance(encoder, EM43BinaryEncoder):
                    output_val = encoder.decode_output(final_space, len(genome.programme))
                else:  # EM43PositionalEncoder
                    output_val = encoder.decode_output(final_space, len(genome.programme), input_val)

                if output_val >= 0 and output_val == target_val:
                    correct += 1

            accuracy = correct / len(inputs)

            # Add sparsity penalty
            sparsity_penalty = 0.01 * np.count_nonzero(genome.programme) / len(genome.programme)

            return accuracy - sparsity_penalty

        except Exception as e:
            print(f"Error evaluating genome: {e}")
            return -1.0

    return fitness_fn


def create_unified_validation_function(encoder, test_inputs, test_targets):
    """Create validation function that works with any encoder"""
    def validate(genome, sim):
        try:
            # Create all initial spaces at once
            initial_spaces = []
            for input_val in test_inputs:
                initial_space = encoder.encode_input(
                    genome.programme, input_val, sim.window
                )
                initial_spaces.append(initial_space)

            # Batch simulate all spaces
            final_spaces = sim.simulate_spaces(genome, initial_spaces)

            # Decode all outputs
            correct = 0
            for i, (input_val, target_val) in enumerate(zip(test_inputs, test_targets)):
                final_space = final_spaces[i]

                if isinstance(encoder, EM43BinaryEncoder):
                    output_val = encoder.decode_output(final_space, len(genome.programme))
                else:  # EM43PositionalEncoder
                    output_val = encoder.decode_output(final_space, len(genome.programme), input_val)

                if output_val >= 0 and output_val == target_val:
                    correct += 1

            return correct / len(test_inputs)

        except Exception as e:
            print(f"Validation error: {e}")
            return 0.0

    return validate


def main():
    """Main training loop"""
    parser = argparse.ArgumentParser(description='Train EM-4/3 increment with configurable encoding')

    # Core parameters
    parser.add_argument('--encoding', choices=['binary', 'positional'], default='binary',
                       help='Encoding method to use')
    parser.add_argument('--generations', type=int, default=100,
                       help='Number of generations (default: 100)')
    parser.add_argument('--population', type=int, default=300,
                       help='Population size (default: 300)')

    # CA parameters
    parser.add_argument('--programme-length', type=int, default=None,
                       help='Programme length (default: 8 for binary, 6 for positional)')
    parser.add_argument('--window-size', type=int, default=None,
                       help='Window size (default: 120 for binary, 200 for positional)')
    parser.add_argument('--max-steps', type=int, default=150,
                       help='Maximum simulation steps (default: 150)')
    parser.add_argument('--halt-thresh', type=float, default=0.50,
                       help='Halt threshold for simulation (default: 0.50)')

    # Genetic algorithm parameters
    parser.add_argument('--mutation-rate', type=float, default=0.05,
                       help='Mutation rate (default: 0.05)')
    parser.add_argument('--programme-mutation-rate', type=float, default=0.10,
                       help='Programme mutation rate (default: 0.10)')
    parser.add_argument('--elite-fraction', type=float, default=0.15,
                       help='Elite fraction (default: 0.15)')
    parser.add_argument('--tournament-size', type=int, default=3,
                       help='Tournament size (default: 3)')
    parser.add_argument('--random-immigrant-rate', type=float, default=0.25,
                       help='Random immigrant rate (default: 0.25)')

    # Input range parameters
    parser.add_argument('--input-range-start', type=int, default=None,
                       help='Input range start (default: 1)')
    parser.add_argument('--input-range-end', type=int, default=None,
                       help='Input range end (default: 15 for binary, 20 for positional)')

    # Early stopping parameters
    parser.add_argument('--early-stopping-threshold', type=float, default=1.0,
                       help='Early stopping accuracy threshold (default: 1.0)')
    parser.add_argument('--disable-early-stopping', action='store_true',
                       help='Disable early stopping')

    # Training parameters
    parser.add_argument('--checkpoint-every', type=int, default=25,
                       help='Checkpoint frequency in generations (default: 25)')

    # Binary encoder parameters
    parser.add_argument('--bit-width', type=int, default=8,
                       help='Bit width for binary encoding (default: 8)')
    parser.add_argument('--input-state', type=int, default=2,
                       help='Input state for binary encoding (default: 2)')

    # Positional encoder parameters
    parser.add_argument('--beacon-state', type=int, default=2,
                       help='Beacon state for positional encoding (default: 2)')
    parser.add_argument('--separator-state', type=int, default=3,
                       help='Separator state for positional encoding (default: 3)')

    args = parser.parse_args()
    
    print(f"EM-4/3 Increment Task Training ({args.encoding.upper()} encoding)")
    print("=" * 60)

    # Set encoding-specific defaults if not provided
    if args.programme_length is None:
        args.programme_length = 8 if args.encoding == 'binary' else 6
    if args.window_size is None:
        args.window_size = 120 if args.encoding == 'binary' else 200
    if args.input_range_start is None:
        args.input_range_start = 1
    if args.input_range_end is None:
        args.input_range_end = 15 if args.encoding == 'binary' else 20

    # Create encoder based on choice
    if args.encoding == 'binary':
        encoder = EM43BinaryEncoder(bit_width=args.bit_width, input_state=args.input_state)
        print("Using binary encoding: numbers → binary → state mapping")
        print(f"Example: 5 → 00000101 → [0,0,0,0,0,{args.input_state},0,{args.input_state}]")
        print(f"Bit width: {args.bit_width}, Input state: {args.input_state}")
    else:
        encoder = EM43PositionalEncoder(beacon_state=args.beacon_state, separator_state=args.separator_state)
        print("Using positional encoding: numbers → positional → beacon placement")
        print(f"Example: 5 → [0,0,0,0,0,{args.beacon_state}] (5 zeros + beacon)")
        print(f"Beacon state: {args.beacon_state}, Separator state: {args.separator_state}")

    # Configuration from CLI arguments
    config = {
        'population_size': args.population,
        'generations': args.generations,
        'programme_length': args.programme_length,
        'window_size': args.window_size,
        'max_steps': args.max_steps,
        'halt_thresh': args.halt_thresh,
        'mutation_rate': args.mutation_rate,
        'programme_mutation_rate': args.programme_mutation_rate,
        'elite_fraction': args.elite_fraction,
        'tournament_size': args.tournament_size,
        'random_immigrant_rate': args.random_immigrant_rate,
        'input_range': (args.input_range_start, args.input_range_end),
        'early_stopping_threshold': args.early_stopping_threshold,
        'disable_early_stopping': args.disable_early_stopping,
        'checkpoint_every': args.checkpoint_every
    }
    
    print(f"\nConfiguration:")
    print(f"- Population size: {config['population_size']}")
    print(f"- Generations: {config['generations']}")
    print(f"- Programme length: {config['programme_length']}")
    print(f"- Window size: {config['window_size']}")
    print(f"- Max steps: {config['max_steps']}")
    print(f"- Halt threshold: {config['halt_thresh']}")
    print(f"- Input range: {config['input_range']}")
    print(f"- Mutation rate: {config['mutation_rate']}")
    print(f"- Programme mutation rate: {config['programme_mutation_rate']}")
    print(f"- Elite fraction: {config['elite_fraction']}")
    print(f"- Tournament size: {config['tournament_size']}")
    print(f"- Random immigrant rate: {config['random_immigrant_rate']}")
    if not config['disable_early_stopping']:
        print(f"- Early stopping threshold: {config['early_stopping_threshold']}")
    print(f"- Checkpoint every: {config['checkpoint_every']} generations")
    print()
    
    # Create simulator
    simulator = NumbaSimulator(
        window=config['window_size'],
        max_steps=config['max_steps'],
        halt_thresh=config['halt_thresh']
    )
    
    # Create optimizer
    optimizer = GAOptimizer(
        population_size=config['population_size'],
        mutation_rate=config['mutation_rate'],
        programme_mutation_rate=config['programme_mutation_rate'],
        elite_fraction=config['elite_fraction'],
        tournament_size=config['tournament_size'],
        random_immigrant_rate=config['random_immigrant_rate']
    )
    
    # Initialize population
    print("Initializing population...")
    optimizer.initialize_population(programme_length=config['programme_length'])
    
    # Create fitness function
    fitness_fn = create_unified_fitness_function(
        simulator, encoder, config['input_range']
    )
    
    # Create validation function for early stopping
    test_inputs = list(range(1, 8))
    test_targets = [x + 1 for x in test_inputs]
    validation_fn = create_unified_validation_function(
        encoder, test_inputs, test_targets
    )
    
    # Create trainer
    trainer = CATrainer(simulator, optimizer, HammingLoss(), verbose=True)
    
    # Training with configurable early stopping
    print("Starting training...")
    if not config['disable_early_stopping']:
        print(f"Early stopping enabled: will stop if {config['early_stopping_threshold']:.1%} accuracy is reached")
    else:
        print("Early stopping disabled")
    start_time = time.time()

    if config['disable_early_stopping']:
        trainer.fit(
            fitness_fn=fitness_fn,
            epochs=config['generations'],
            checkpoint_every=config['checkpoint_every']
        )
    else:
        trainer.fit(
            fitness_fn=fitness_fn,
            epochs=config['generations'],
            checkpoint_every=config['checkpoint_every'],
            early_stopping_threshold=config['early_stopping_threshold'],
            early_stopping_metric="accuracy",
            validation_fn=validation_fn
        )
    
    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time:.2f} seconds")
    
    # Test the best genome
    best_genome = trainer.best_genome
    print(f"\nBest fitness: {trainer.best_fitness:.4f}")
    print(f"Best genome programme: {best_genome.programme}")
    
    # Test on full range
    print(f"\nTesting best genome with {args.encoding} encoding:")
    test_inputs_full = list(range(1, 16))
    
    print("Input -> Expected -> Actual -> Status")
    print("-" * 35)
    
    correct = 0
    for inp in test_inputs_full:
        expected = inp + 1
        
        try:
            # Use modular architecture for testing
            initial_space = encoder.encode_input(best_genome.programme, inp, simulator.window)
            final_spaces = simulator.simulate_spaces(best_genome, [initial_space])
            final_space = final_spaces[0]
            
            if isinstance(encoder, EM43BinaryEncoder):
                out = encoder.decode_output(final_space, len(best_genome.programme))
            else:
                out = encoder.decode_output(final_space, len(best_genome.programme), inp)
            
            status = "✓" if out == expected else "✗"
            if out == expected:
                correct += 1
            print(f"{inp:2d} -> {expected:2d} -> {out:2d} {status}")
            
        except Exception as e:
            print(f"{inp:2d} -> {expected:2d} -> ERR {e}")
    
    accuracy = correct / len(test_inputs_full)
    print(f"\nAccuracy: {accuracy:.2%}")
    
    # Save results
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    filename = f"best_{args.encoding}_increment_genome.json"
    save_genome(best_genome, results_dir / filename)
    print(f"Saved best genome to {results_dir / filename}")
    
    # Plot fitness curve
    plot_filename = f"{args.encoding}_increment_fitness_curve.png"
    plot_fitness_curve(trainer.history, save_path=results_dir / plot_filename)
    print(f"Saved fitness curve to {results_dir / plot_filename}")
    
    return best_genome, trainer.history


if __name__ == "__main__":
    main()
